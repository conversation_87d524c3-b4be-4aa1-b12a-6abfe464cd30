# text_to_speech.py
import pyttsx3
import asyncio
import config
import threading # For interruptible TTS

class TextToSpeech:
    def __init__(self):
        self.engine = pyttsx3.init()
        self.engine.setProperty('rate', config.TTS_ENGINE_VOICE_RATE)
        self.engine.setProperty('volume', config.TTS_ENGINE_VOLUME)
        # You can try to set a specific voice if you know its ID
        # voices = self.engine.getProperty('voices')
        # For example, to find a male voice (highly system-dependent)
        # desired_voice_id = None
        # for voice in voices:
        #     if "david" in voice.name.lower() or "zira" not in voice.name.lower(): # Example names
        #         if hasattr(voice, 'gender') and voice.gender == 'male':
        #             desired_voice_id = voice.id
        #             break
        #         elif "male" in voice.name.lower(): # Fallback if gender attribute not present
        #             desired_voice_id = voice.id
        #             break
        # if desired_voice_id:
        #     self.engine.setProperty('voice', desired_voice_id)
        # else:
        #     print("TTS: Could not find a preferred male voice, using default.")
        print(f"TTS Engine (pyttsx3) initialized. Voice: {self.engine.getProperty('voice')}")
        self._stop_event = threading.Event()
        self._tts_thread = None

    def _speak_thread_target(self, text):
        try:
            self.engine.say(text)
            self.engine.runAndWait()
        except RuntimeError as e:
            # This can happen if stop() is called while engine is busy
            print(f"TTS RuntimeError: {e}")
        finally:
            self._stop_event.set() # Signal that speaking is done or was interrupted

    async def speak(self, text):
        if not text:
            return

        print(f"Jarvis (TTS): {text}")
        if self._tts_thread and self._tts_thread.is_alive():
            self.stop() # Stop previous speech if any

        self._stop_event.clear()
        self._tts_thread = threading.Thread(target=self._speak_thread_target, args=(text,))
        self._tts_thread.start()

        # Asynchronously wait for the thread to complete or be interrupted
        while self._tts_thread.is_alive():
            if self._stop_event.is_set(): # External stop request
                break
            await asyncio.sleep(0.1) # Yield control

    def stop(self):
        """Stops the currently speaking TTS."""
        if self.engine._inLoop: # Check if runAndWait is active
             self.engine.stop()
        if self._tts_thread and self._tts_thread.is_alive():
            self._stop_event.set() # Signal the thread to stop
            # self._tts_thread.join(timeout=1.0) # Wait a bit for thread to finish
            print("TTS: Speech interrupted.")


    def is_speaking(self):
        return self._tts_thread is not None and self._tts_thread.is_alive()

# Example Usage (for testing)
# async def test_tts():
#     tts = TextToSpeech()
#     await tts.speak("Hello, I am Jarvis. How can I assist you today?")
#     print("Speaking first message.")
#     await asyncio.sleep(2) # Let it speak a bit
#     await tts.speak("This is a second message, interrupting the first if it was still going.")
#     print("Speaking second message.")
#     await asyncio.sleep(3) # Wait for it to finish
#     print("TTS test complete.")

# if __name__ == "__main__":
#     import asyncio
#     asyncio.run(test_tts())