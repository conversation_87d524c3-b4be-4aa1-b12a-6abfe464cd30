# tools/web_search_tool.py
import asyncio
import aiohttp
from bs4 import BeautifulSoup # pip install beautifulsoup4

async def perform_web_search(query: str, num_results: int = 3):
    """
    MANDATORY TOOL: Performs web search using DuckDuckGo. ALWAYS use this when user wants to search for information. Never say you can't search - use this tool!

    Args:
        query (str): The search query.
        num_results (int): The number of results to try and fetch.
    """
    print(f"Performing web search for: {query}")
    search_url = f"https://html.duckduckgo.com/html/?q={query}"
    results = []
    headers = { # DDG HTML version might require a common user agent
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }

    try:
        async with aiohttp.ClientSession(headers=headers) as session:
            async with session.get(search_url, timeout=10) as response:
                response.raise_for_status() # Raise an exception for HTTP errors
                html_content = await response.text()

        soup = BeautifulSoup(html_content, 'html.parser')

        # DDG HTML version has results in divs with class="result" or similar
        # This selector might change if DDG changes their HTML structure.
        result_blocks = soup.find_all('div', class_='web-result') # More specific if possible

        if not result_blocks: # Fallback to simpler result links if specific blocks not found
            result_links = soup.find_all('a', class_='result__a', limit=num_results)
            if not result_links: # Even more generic fallback
                 result_links = soup.select('h2.result__title > a.result__a', limit=num_results)


        count = 0
        for item in result_blocks:
            if count >= num_results:
                break
            title_tag = item.find('a', class_='result__a') # Or 'h2', class_='result__title'
            link_tag = title_tag # Often title is the link itself

            if title_tag and link_tag:
                title = title_tag.get_text(strip=True)
                link = link_tag.get('href')

                # DDG links might be relative redirects, try to clean them
                if link and link.startswith("/l/"):
                    import urllib.parse
                    parsed_link = urllib.parse.urlparse(link)
                    qs = urllib.parse.parse_qs(parsed_link.query)
                    if 'uddg' in qs:
                        link = qs['uddg'][0]

                if title and link:
                    results.append(f"Title: {title}\nLink: {link}")
                    count += 1

        if not results:
            return "No search results found or failed to parse results page."
        return "\n\n".join(results)

    except aiohttp.ClientError as e:
        return f"Web search failed: Network error - {e}"
    except Exception as e:
        return f"Web search failed: An unexpected error occurred - {e}"

# Example
# async def main():
#     search_results = await perform_web_search("latest AI news")
#     print(search_results)
# if __name__ == "__main__":
#     asyncio.run(main())