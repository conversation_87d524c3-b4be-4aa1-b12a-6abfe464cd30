# llm_handler.py
import asyncio
import aiohttp
import json
import config
from conversation_manager import ConversationManager
from tool_registry import ToolRegistry

class LLMHandler:
    def __init__(self, api_key, model_name, api_url, conversation_manager: ConversationManager, tool_registry: ToolRegistry):
        self.api_key = api_key
        self.model_name = model_name
        self.api_url = api_url
        self.conversation_manager = conversation_manager
        self.tool_registry = tool_registry
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }

    def _get_system_prompt(self) -> str:
        """Get the system prompt for <PERSON>."""
        return """You are <PERSON>, <PERSON>'s AI assistant. You MUST use the available tools to complete tasks.

🚨 CRITICAL TOOL USAGE RULES - FOLLOW THESE EXACTLY:

1. **TIME QUESTIONS**: If user asks about time/date → IMMEDIATELY call get_current_time tool
2. **SEARCH REQUESTS**: If user wants to search/find information → IMMEDIATELY call perform_web_search tool
3. **APP LAUNCH**: If user wants to open/launch apps → IMMEDIATELY call launch_application tool
4. **MULTIPLE REQUESTS**: If user asks for multiple things → call ALL relevant tools in sequence

⚠️ NEVER say "I don't know the time" or "I can't search" - YOU HAVE TOOLS FOR THESE!

🎯 MANDATORY BEHAVIOR:
- User asks "What time is it?" → MUST call get_current_time
- User asks "Search for X" → MUST call perform_web_search with query "X"
- User asks "Open notepad" → MUST call launch_application with "notepad"
- User asks "What time is it and search for AI news?" → MUST call BOTH get_current_time AND perform_web_search

📋 EXAMPLES OF CORRECT BEHAVIOR:

❌ WRONG: "I don't know the current time, please check your device"
✅ CORRECT: [calls get_current_time tool] "The current time is..."

❌ WRONG: "I can suggest some AI news sources"
✅ CORRECT: [calls perform_web_search with "AI news today"] "I found these AI news articles..."

❌ WRONG: "You can open notepad from your start menu"
✅ CORRECT: [calls launch_application with "notepad"] "I'm launching notepad for you"

🔥 YOU ARE AN ACTION-ORIENTED AI AGENT:
- Don't just talk about doing things - DO THEM with tools
- Be proactive and complete tasks fully
- Use multiple tools when needed
- Always try to accomplish the user's full request

Remember: You have powerful tools - USE THEM! Don't be a passive chatbot, be an active assistant that gets things done."""

    async def get_response(self, user_input=None):
        """
        Gets a response from the LLM, handling potential tool calls.

        Args:
            user_input (str, optional): The latest input from the user.
                                        If None, it might be a follow-up after a tool call.

        Returns:
            str: The LLM's text response to be spoken or displayed.
        """
        if user_input:
            self.conversation_manager.add_message("user", user_input)

        messages = self.conversation_manager.get_formatted_history(include_context=False)
        llm_tools = self.tool_registry.get_llm_tool_schemas()

        # Combine system prompt with context
        system_prompt = self._get_system_prompt()
        context_summary = self.conversation_manager.get_context_summary()

        if context_summary:
            combined_system_prompt = f"{system_prompt}\n\nCONTEXT: {context_summary}"
        else:
            combined_system_prompt = system_prompt

        # Replace or add system message
        if messages and messages[0]["role"] == "system":
            messages[0]["content"] = combined_system_prompt
        else:
            messages.insert(0, {"role": "system", "content": combined_system_prompt})

        payload = {
            "model": self.model_name,
            "messages": messages,
        }
        if llm_tools: # Only add tools if there are any registered
            payload["tools"] = llm_tools
            payload["tool_choice"] = "auto" # Let the LLM decide if it wants to use a tool

        print(f"LLM Request Payload: {json.dumps(payload, indent=2)}")

        async with aiohttp.ClientSession() as session:
            try:
                async with session.post(self.api_url, headers=self.headers, json=payload) as resp:
                    if resp.status == 200:
                        response_data = await resp.json()
                        print(f"LLM Raw Response: {json.dumps(response_data, indent=2)}")

                        # Check for an error field in the response first
                        if "error" in response_data:
                            error_message = response_data["error"].get("message", "Unknown LLM API error")
                            print(f"LLM API Error: {error_message}")
                            self.conversation_manager.add_message("assistant", f"Sorry, I encountered an API error: {error_message}")
                            return f"Sorry, I encountered an API error. Please check the logs."

                        if not response_data.get("choices") or not response_data["choices"][0].get("message"):
                            print("LLM response malformed: Missing choices or message.")
                            self.conversation_manager.add_message("assistant", "Sorry, I received an unexpected response from the AI.")
                            return "I seem to have trouble formulating a response right now."

                        assistant_message = response_data["choices"][0]["message"]

                        if assistant_message.get("tool_calls"):
                            # Handle tool calls
                            tool_calls = assistant_message["tool_calls"]

                            # Add the assistant message with tool calls to conversation
                            self.conversation_manager.add_message("assistant", assistant_message.get("content") or "", tool_calls=tool_calls)

                            # Execute each tool and collect results
                            for tool_call in tool_calls:
                                tool_name = tool_call["function"]["name"]
                                tool_args_str = tool_call["function"]["arguments"]
                                tool_id = tool_call["id"]

                                # Call the tool
                                print(f"LLM requests tool: {tool_name} with args: {tool_args_str}")
                                tool_result = await self.tool_registry.call_tool(tool_name, tool_args_str)
                                print(f"Tool '{tool_name}' result: {tool_result}")

                                # Add tool result to conversation history
                                tool_message = {
                                    "role": "tool",
                                    "content": str(tool_result) if not isinstance(tool_result, str) else tool_result,
                                    "tool_call_id": tool_id,
                                    "name": tool_name
                                }
                                self.conversation_manager.history.append(tool_message)

                            # Make a new call to the LLM with the tool results to get final response
                            return await self.get_response() # Recursive call to get final text response

                        else:
                            # No tool calls, just a text response
                            response_text = assistant_message.get("content", "")
                            self.conversation_manager.add_message("assistant", response_text)
                            return response_text

                    else:
                        error_text = await resp.text()
                        print(f"Error from OpenRouter API: {resp.status} - {error_text}")
                        error_detail = f"API request failed with status {resp.status}. Details: {error_text[:200]}..."
                        self.conversation_manager.add_message("assistant", f"Sorry, I couldn't reach my brain. {error_detail}")
                        return f"Sorry, there was an issue with the AI service ({resp.status})."
            except aiohttp.ClientConnectorError as e:
                print(f"Network error connecting to OpenRouter: {e}")
                self.conversation_manager.add_message("assistant", "Sorry, I'm having trouble connecting to the network.")
                return "I can't connect to my services right now. Please check your internet connection."
            except Exception as e:
                print(f"An unexpected error occurred in LLMHandler: {e}")
                self.conversation_manager.add_message("assistant", "I ran into an unexpected problem.")
                return "An unexpected error occurred while processing your request."