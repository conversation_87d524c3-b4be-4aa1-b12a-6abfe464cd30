import os
from dotenv import load_dotenv

load_dotenv()

# --- API Keys ---
OPENROUTER_API_KEY = os.getenv("OPENROUTER_API_KEY")
PORCUPINE_API_KEY = os.getenv("PORCUPINE_API_KEY") # Can be None if not using Porcupine or if it doesn't require a key for basic models

# --- LLM Configuration ---
# Using the original working model
OPENROUTER_MODEL_NAME = "google/gemini-flash-1.5"  # Works with function calling
OPENROUTER_API_URL = "https://openrouter.ai/api/v1/chat/completions"

# --- Wake Word Configuration ---
WAKE_WORDS = ["jarvis", "hey jarvis"] # Porcupine uses .ppn keyword files for custom wake words. For simplicity, we'll use generic keywords if available or simply listen actively after a prompt for this example.
# If using pvporcupine, you'd specify paths to .ppn files.
# For this example, we'll simulate wake word or use a simpler "always listening" for demonstration if pvporcupine setup is too complex for the initial code.

# --- Speech-to-Text (STT) Configuration ---
VOSK_MODEL_PATH = "vosk_model/vosk-model-small-en-us-0.15" # Path to your downloaded Vosk model directory

# --- Text-to-Speech (TTS) Configuration ---
TTS_ENGINE_VOICE_RATE = 180 # Words per minute
TTS_ENGINE_VOLUME = 1.0
# For pyttsx3, you might want to select a specific voice if available on your system
# voices = engine.getProperty('voices')
# engine.setProperty('voice', voices[0].id) # Example: first available voice

# --- Audio Cues ---
SOUND_LISTENING_START = "sounds/listening_start.wav" # Path to a short 'ting' sound
SOUND_LISTENING_STOP = "sounds/listening_stop.wav"   # Optional

# --- Conversation Settings ---
CONVERSATION_TIMEOUT_SECONDS = 20 # Time of silence before requiring wake word again
ACTIVE_LISTEN_DURATION_AFTER_TTS = 5 # Seconds to listen without wake word after Jarvis speaks

# --- Memory Configuration ---
SHORT_TERM_MEMORY_SIZE = 50  # Number of recent interactions to keep
LONG_TERM_MEMORY_FILE = "memory/long_term_memory.json"
CONTEXT_MEMORY_FILE = "memory/context_memory.json"

# --- Task Management ---
TASK_LOG_FILE = "tasks/task_log.json"
TASK_SCHEDULE_FILE = "tasks/scheduled_tasks.json"

# --- File Operations ---
ALLOWED_FILE_EXTENSIONS = [".txt", ".md", ".py", ".js", ".json", ".csv", ".log"]
MAX_FILE_SIZE_MB = 10

# --- Research Configuration ---
MAX_SEARCH_RESULTS = 10
RESEARCH_CACHE_DIR = "cache/research"

# --- Communication ---
WHATSAPP_WEB_TIMEOUT = 30  # seconds

# --- Other ---
LOG_FILE_PATH = "jarvis_log.txt"
DEBUG_MODE = True