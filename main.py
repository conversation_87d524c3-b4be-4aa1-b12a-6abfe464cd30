# main.py
import asyncio
import signal # For graceful shutdown
import config # Your config file

# Core components
from speech_to_text import SpeechToText
from text_to_speech import TextToSpeech
from conversation_manager import ConversationManager
from llm_handler import LLMHandler
from tool_registry import ToolRegistry

# Enhanced imports for memory and agents
from memory.short_term_memory import ShortTermMemory
from memory.long_term_memory import LongTermMemory

# --- Global State ---
stt_engine = None
tts_engine = None
convo_manager = None
tool_reg = None
llm_handler = None
main_task_loop = None
stop_event = asyncio.Event()
is_listening_actively = False # To manage when STT should be active without wake word
last_interaction_time = asyncio.get_event_loop().time()

# --- Audio Cue (Simple Placeholder) ---
async def play_sound(sound_type="start"):
    # In a real app, you'd use a library like playsound or pygame to play a .wav file
    if sound_type == "start":
        print("\n*ding* (Listening...)\n") # Placeholder
    elif sound_type == "stop":
        print("\n*dong* (Processing...)\n") # Placeholder
    await asyncio.sleep(0.1) # Simulate sound playing

# --- Core Logic ---
async def initialize_systems():
    global stt_engine, tts_engine, convo_manager, tool_reg, llm_handler

    print("Initializing Jarvis Systems...")
    stt_engine = SpeechToText(model_path=config.VOSK_MODEL_PATH)
    if not stt_engine.recognizer:
        print("FATAL: STT Engine failed to initialize. Exiting.")
        return False

    tts_engine = TextToSpeech()
    convo_manager = ConversationManager()
    tool_reg = ToolRegistry()

    # Register all available tools
    tool_reg.register_all_tools()

    llm_handler = LLMHandler(
        api_key=config.OPENROUTER_API_KEY,
        model_name=config.OPENROUTER_MODEL_NAME,
        api_url=config.OPENROUTER_API_URL,
        conversation_manager=convo_manager,
        tool_registry=tool_reg
    )
    if not config.OPENROUTER_API_KEY:
        print("WARNING: OPENROUTER_API_KEY is not set in .env or config.py. LLM will not work.")
        # await tts_engine.speak("Warning: My connection to the OpenRouter API is not configured.")
    print("Jarvis Systems Initialized.")
    return True

async def handle_text_input(text_input):
    global last_interaction_time, is_listening_actively
    if not text_input or text_input.strip().lower() == "exit":
        return False # Signal to exit loop or ignore

    # Don't print "You:" here since it's already printed in the text loop
    await play_sound("start") # Indicate processing
    print("\n*dong* (Processing...)")
    is_listening_actively = False

    response_text = await llm_handler.get_response(text_input)
    if response_text:
        print(f"Jarvis: {response_text}")
        # Start TTS in background without waiting
        asyncio.create_task(tts_engine.speak(response_text))
    else:
        error_msg = "I didn't get a response. Please try again."
        print(f"Jarvis: {error_msg}")
        # Start TTS in background without waiting
        asyncio.create_task(tts_engine.speak(error_msg))

    last_interaction_time = asyncio.get_event_loop().time()
    is_listening_actively = True # Listen for a short period after TTS
    return True


class VoiceController:
    def __init__(self):
        self.is_recording = False
        self.recording_task = None

    async def start_recording(self):
        """Start voice recording when Caps Lock is pressed."""
        if self.is_recording:
            return

        self.is_recording = True
        await play_sound("start")
        print("🎤 Recording... (Hold Caps Lock)")

        # Start recording in background
        self.recording_task = asyncio.create_task(self._record_voice())

    async def stop_recording(self):
        """Stop voice recording when Caps Lock is released."""
        if not self.is_recording:
            return

        self.is_recording = False
        await play_sound("stop")
        print("🎤 Processing voice...")

        # Wait for recording to finish and get result
        if self.recording_task:
            try:
                transcribed_text = await self.recording_task
                if transcribed_text and transcribed_text.strip():
                    print(f"You (voice): {transcribed_text}")

                    # Process the voice command
                    response_text = await llm_handler.get_response(transcribed_text)
                    if response_text:
                        print(f"Jarvis (voice): {response_text}")
                        # Start TTS in background without waiting
                        asyncio.create_task(tts_engine.speak(response_text))
                    else:
                        error_msg = "I didn't understand that. Could you try again?"
                        print(f"Jarvis (voice): {error_msg}")
                        asyncio.create_task(tts_engine.speak(error_msg))
                else:
                    print("🎤 No speech detected.")

            except Exception as e:
                print(f"Voice processing error: {e}")
            finally:
                self.recording_task = None

    async def _record_voice(self):
        """Record voice while caps lock is held."""
        try:
            # Record for up to 10 seconds or until caps lock is released
            transcribed_text = await stt_engine.listen_and_transcribe(
                duration_seconds=10,
                silence_threshold_seconds=2.0
            )
            return transcribed_text.strip() if transcribed_text else ""

        except Exception as e:
            print(f"Recording error: {e}")
            return ""

# Global voice controller
voice_controller = VoiceController()

async def caps_lock_callback(is_pressed):
    """Callback for caps lock press/release events."""
    try:
        if is_pressed:
            await voice_controller.start_recording()
        else:
            await voice_controller.stop_recording()
    except Exception as e:
        print(f"Caps lock callback error: {e}")

async def voice_interaction_loop():
    """Initialize voice system with push-to-talk."""
    from keyboard_listener import KeyboardListener

    print("\n🎤 Voice Mode: Hold Caps Lock to talk to Jarvis.")

    # Set up keyboard listener
    keyboard_listener = KeyboardListener()
    keyboard_listener.set_caps_lock_callback(caps_lock_callback)
    keyboard_listener.start()

    try:
        # Keep the voice loop running
        while not stop_event.is_set():
            await asyncio.sleep(1)
    finally:
        keyboard_listener.stop()


async def text_input_loop():
    global last_interaction_time, is_listening_actively
    loop = asyncio.get_event_loop()

    print("\n=== JARVIS READY ===")
    print("Type your messages below. Type 'exit' or 'quit' to stop.")
    print("Available commands: 'help', 'status', 'clear', 'memory'")
    print("=" * 50)

    while not stop_event.is_set():
        try:
            # Use asyncio.to_thread for blocking input() call
            text_input = await loop.run_in_executor(None, lambda: input("\nYou: ").strip())

            if not text_input:  # Skip empty inputs
                continue

            if text_input.lower() in ["exit", "quit", "bye", "goodbye"]:
                print("Jarvis: Goodbye! Saving conversation summary...")
                convo_manager.end_conversation_summary()
                stop_event.set()
                break

            # Handle special commands
            if text_input.lower() == "help":
                help_text = """
Available Commands:
- help: Show this help message
- status: Show system status
- clear: Clear conversation history
- memory: Show memory statistics
- tools: List available tools
- exit/quit/bye: Exit Jarvis

You can also ask me to:
- Get current time
- Search the web
- Launch applications
- Read/write files
- Research topics
- Manage tasks
- Control system
- And much more!
                """
                print(help_text)
                continue

            elif text_input.lower() == "status":
                stats = convo_manager.get_conversation_stats()
                print(f"\nJarvis Status:")
                print(f"  Conversation started: {convo_manager.conversation_started}")
                print(f"  Total interactions: {stats.get('total_interactions', 0)}")
                print(f"  Active tasks: {stats.get('active_tasks_count', 0)}")
                print(f"  Available tools: {len(tool_reg.list_tools())}")
                continue

            elif text_input.lower() == "clear":
                convo_manager.clear_history()
                print("Jarvis: Conversation history cleared.")
                continue

            elif text_input.lower() == "memory":
                ltm_summary = convo_manager.long_term_memory.get_memory_summary()
                print(f"\nMemory Summary:\n{ltm_summary}")
                continue

            elif text_input.lower() == "tools":
                tools_list = tool_reg.list_tools()
                print(f"\nAvailable Tools ({len(tools_list)}):")
                for i, tool in enumerate(tools_list, 1):
                    print(f"  {i}. {tool}")
                continue

            # Handle interruption if TTS is speaking
            if tts_engine.is_speaking():
                print("(Interrupting Jarvis...)")
                tts_engine.stop()

            # Process the input
            last_interaction_time = asyncio.get_event_loop().time()
            success = await handle_text_input(text_input)

            # Don't break on success - continue the loop
            if not success:
                print("Jarvis: Goodbye!")
                break

        except (EOFError, KeyboardInterrupt):
            print("\nJarvis: Received interrupt signal. Goodbye!")
            convo_manager.end_conversation_summary()
            stop_event.set()
            break
        except Exception as e:
            print(f"Error in text input loop: {e}")
            await asyncio.sleep(0.1)

    print("Text input loop ended.")


async def main():
    global main_task_loop
    if not await initialize_systems():
        return

    # Start the text input loop as a concurrent task
    # For voice, you'd have a wake word loop here.
    # Since we simplified wake word, text input is primary, voice is secondary/experimental.

    text_loop_task = asyncio.create_task(text_input_loop())
    # voice_loop_task = asyncio.create_task(voice_interaction_loop()) # Disable voice temporarily

    main_task_loop = asyncio.gather(text_loop_task) # Text only for now

    try:
        await main_task_loop
    except asyncio.CancelledError:
        print("Main loop cancelled.")
    finally:
        print("Shutting down Jarvis...")
        if stt_engine:
            stt_engine.cleanup()
        # TTS cleanup not explicitly needed for pyttsx3 usually
        print("Jarvis shutdown complete.")


def signal_handler(sig, frame):
    print(f"Signal {sig} received, initiating shutdown...")
    stop_event.set()
    if main_task_loop:
        main_task_loop.cancel()

if __name__ == "__main__":
    signal.signal(signal.SIGINT, signal_handler) # Handle Ctrl+C
    signal.signal(signal.SIGTERM, signal_handler) # Handle kill

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("KeyboardInterrupt caught in asyncio.run, shutting down.")
        stop_event.set()
    except Exception as e:
        print(f"Unhandled exception in main: {e}")
    finally:
        # This ensures that even if asyncio.run exits abruptly,
        # we attempt to signalize stop for any pending tasks.
        if not stop_event.is_set():
            stop_event.set()
        # Give a moment for tasks to acknowledge cancellation if gather isn't fully awaited
        # This part is tricky; proper cleanup within tasks is better.
        print("Exiting application.")