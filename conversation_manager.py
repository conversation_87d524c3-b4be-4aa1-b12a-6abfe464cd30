# conversation_manager.py
import asyncio
from datetime import datetime
from typing import List, Dict, Any, Optional
from memory.short_term_memory import ShortTermMemory
from memory.long_term_memory import LongTermMemory

class ConversationManager:
    def __init__(self, max_history_len=10):
        self.history = []
        self.max_history_len = max_history_len
        self.short_term_memory = ShortTermMemory()
        self.long_term_memory = LongTermMemory()
        self.conversation_started = datetime.now()
        self.current_topic = None
        self.user_name = None

    def add_message(self, role, content, metadata=None, tool_calls=None):
        """Adds a message to the history and memory systems.
        Args:
            role (str): "user" or "assistant" or "tool"
            content (str): The message content.
            metadata (dict): Optional metadata about the message.
            tool_calls (list): Optional tool calls for assistant messages.
        """
        message = {"role": role, "content": content}
        if metadata:
            message.update(metadata)
        if tool_calls:
            message["tool_calls"] = tool_calls

        self.history.append(message)

        # Add to short-term memory
        self.short_term_memory.add_interaction(role, content, metadata)

        # Detect and update context
        self._update_context(role, content)

        # Keep only the most recent messages in history
        if len(self.history) > self.max_history_len * 2:
            self.history = self.history[-self.max_history_len*2:]

    def _update_context(self, role: str, content: str):
        """Update conversation context based on message content."""
        content_lower = content.lower()

        # Detect user name
        if role == "user" and ("my name is" in content_lower or "i'm" in content_lower or "i am" in content_lower):
            # Simple name extraction
            words = content.split()
            for i, word in enumerate(words):
                if word.lower() in ["name", "i'm", "i", "am"] and i + 1 < len(words):
                    potential_name = words[i + 1].strip(".,!?")
                    if potential_name.isalpha() and len(potential_name) > 1:
                        self.user_name = potential_name
                        self.short_term_memory.update_context("user_name", potential_name)
                        self.long_term_memory.add_user_preference("personal", "name", potential_name)
                        break

        # Detect topic changes
        if role == "user":
            # Simple topic detection based on keywords
            topics = ["work", "project", "research", "task", "schedule", "reminder", "file", "email", "message"]
            for topic in topics:
                if topic in content_lower:
                    if self.current_topic != topic:
                        self.current_topic = topic
                        self.short_term_memory.update_context("current_topic", topic)
                    break

    def get_formatted_history(self, include_context=True):
        """Returns the history in a format suitable for LLMs."""
        formatted_history = []

        # Add system message with context if requested
        if include_context:
            context_summary = self.get_context_summary()
            if context_summary:
                formatted_history.append({
                    "role": "system",
                    "content": f"Context: {context_summary}"
                })

        # Add conversation history
        formatted_history.extend(self.history[:])

        return formatted_history

    def get_context_summary(self) -> str:
        """Get a summary of current conversation context."""
        context_parts = []

        # Add user name if known
        if self.user_name:
            context_parts.append(f"User name: {self.user_name}")

        # Add current topic
        if self.current_topic:
            context_parts.append(f"Current topic: {self.current_topic}")

        # Add short-term memory context
        stm_context = self.short_term_memory.get_context_summary()
        if stm_context and stm_context != "No recent conversation history.":
            context_parts.append(stm_context)

        # Add relevant long-term memory
        ltm_summary = self.long_term_memory.get_memory_summary()
        if ltm_summary:
            context_parts.append(f"Memory: {ltm_summary}")

        return " | ".join(context_parts) if context_parts else ""

    def search_conversation_history(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Search through conversation history."""
        return self.short_term_memory.search_interactions(query, limit)

    def add_important_fact(self, fact: str, category: str = "conversation"):
        """Add an important fact to long-term memory."""
        self.long_term_memory.add_important_fact(fact, category)

    def remember_user_preference(self, category: str, preference: str, value: Any):
        """Remember a user preference."""
        self.long_term_memory.add_user_preference(category, preference, value)

    def get_user_preference(self, category: str, preference: str = None):
        """Get a user preference."""
        return self.long_term_memory.get_user_preference(category, preference)

    def end_conversation_summary(self):
        """Create a summary when conversation ends."""
        if len(self.history) > 5:  # Only summarize substantial conversations
            # Extract topics and key points
            topics = []
            key_points = []

            for message in self.history:
                if message["role"] == "user":
                    content = message["content"].lower()
                    # Simple topic extraction
                    if any(word in content for word in ["task", "project", "work"]):
                        topics.append("work/tasks")
                    if any(word in content for word in ["research", "search", "information"]):
                        topics.append("research")
                    if any(word in content for word in ["file", "document", "folder"]):
                        topics.append("files")

                    # Extract potential key points (questions or important statements)
                    if "?" in message["content"] or len(message["content"]) > 50:
                        key_points.append(message["content"][:100])

            # Remove duplicates
            topics = list(set(topics))

            # Create summary
            summary = f"Conversation with {self.user_name or 'user'} covered {', '.join(topics) if topics else 'general topics'}. "
            if key_points:
                summary += f"Key points: {'; '.join(key_points[:2])}."

            # Save to long-term memory
            self.long_term_memory.add_conversation_summary(summary, topics, importance=5)

    def clear_history(self):
        """Clear conversation history but preserve memory."""
        self.end_conversation_summary()  # Save summary before clearing
        self.history = []
        self.short_term_memory.clear_all()
        self.conversation_started = datetime.now()
        self.current_topic = None

    def get_conversation_stats(self) -> Dict[str, Any]:
        """Get statistics about the current conversation."""
        return self.short_term_memory.get_conversation_stats()

# Example
# convo_manager = ConversationManager()
# convo_manager.add_message("user", "Hello Jarvis")
# convo_manager.add_message("assistant", "Hello! How can I help you?")
# print(convo_manager.get_formatted_history())