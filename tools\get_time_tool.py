# tools/get_time_tool.py
import datetime

def get_current_time():
    """
    Gets the current date and time.
    """
    now = datetime.datetime.now()
    return now.strftime("%Y-%m-%d %H:%M:%S")

async def get_current_time_async(): # Example of an async tool
    """
    MANDATORY TOOL: Gets the current date and time. ALWAYS use this when user asks about time/date. Never say you don't know the time - use this tool!
    """
    now = datetime.datetime.now()
    return now.strftime("%A, %B %d, %Y %I:%M %p")

# You would register `get_current_time_async` or `get_current_time`