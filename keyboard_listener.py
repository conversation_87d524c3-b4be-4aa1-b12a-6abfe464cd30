# keyboard_listener.py
import asyncio
import threading
from pynput import keyboard
from pynput.keyboard import Key

class KeyboardListener:
    def __init__(self):
        self.caps_lock_pressed = False
        self.caps_lock_callback = None
        self.listener = None
        self.running = False
        
    def set_caps_lock_callback(self, callback):
        """Set callback function to be called when caps lock state changes.
        Callback receives (is_pressed: bool) parameter."""
        self.caps_lock_callback = callback
        
    def on_press(self, key):
        """Handle key press events."""
        try:
            if key == Key.caps_lock:
                if not self.caps_lock_pressed:
                    self.caps_lock_pressed = True
                    if self.caps_lock_callback:
                        # Run callback in thread to avoid blocking
                        threading.Thread(
                            target=lambda: asyncio.run(self.caps_lock_callback(True)),
                            daemon=True
                        ).start()
        except Exception as e:
            print(f"Keyboard listener error on press: {e}")
            
    def on_release(self, key):
        """Handle key release events."""
        try:
            if key == Key.caps_lock:
                if self.caps_lock_pressed:
                    self.caps_lock_pressed = False
                    if self.caps_lock_callback:
                        # Run callback in thread to avoid blocking
                        threading.Thread(
                            target=lambda: asyncio.run(self.caps_lock_callback(False)),
                            daemon=True
                        ).start()
        except Exception as e:
            print(f"Keyboard listener error on release: {e}")
            
    def start(self):
        """Start the keyboard listener."""
        if self.running:
            return
            
        self.running = True
        self.listener = keyboard.Listener(
            on_press=self.on_press,
            on_release=self.on_release
        )
        self.listener.start()
        print("🎹 Keyboard listener started. Hold Caps Lock to talk to Jarvis.")
        
    def stop(self):
        """Stop the keyboard listener."""
        if self.listener:
            self.listener.stop()
            self.running = False
            print("🎹 Keyboard listener stopped.")
            
    def is_caps_lock_pressed(self):
        """Check if caps lock is currently pressed."""
        return self.caps_lock_pressed
